import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/android_params.dart';
import 'package:flutter_callkit_incoming/entities/call_event.dart';
import 'package:flutter_callkit_incoming/entities/call_kit_params.dart';
import 'package:flutter_callkit_incoming/entities/ios_params.dart';
import 'package:flutter_callkit_incoming/entities/notification_params.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_callkit_incoming_example/app_router.dart';
import 'package:flutter_callkit_incoming_example/navigation_service.dart';
import 'package:flutter_callkit_incoming_example/api_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:uuid/uuid.dart';

final userId = Platform.isAndroid ? 1111 : 2222;


class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return HomePageState();
  }
}

class HomePageState extends State<HomePage> {
  late final Uuid _uuid;
  String? _currentUuid;
  String textEvents = "";
  String? _registerTokenStatus;
  final TextEditingController _calleeController = TextEditingController();
  String? _callStatus;

  @override
  void initState() {
    super.initState();
    _uuid = const Uuid();
    _currentUuid = "";
    textEvents = "";
    initCurrentCall();
    listenerEvent(onEvent);
    _registerPushToken();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plugin example app'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            Column(
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.clear_all_sharp),
                  label: Text('End all calls'),
                  onPressed: endAllCalls,
                ),
                ElevatedButton.icon(
                  icon: const Icon(Icons.security),
                  label: Text('Request permissions'),
                  onPressed: requestAllPermissions,
                ),
                const Divider(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> requestNotificationPermission() async {
    await FlutterCallkitIncoming.requestNotificationPermission({
      "rationaleMessagePermission":
          "Notification permission is required, to show notification.",
      "postNotificationMessageRequired":
          "Notification permission is required, Please allow notification permission from setting."
    });
  }

  Future<void> requestAllPermissions() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text('Requesting permissions...'),
              ],
            ),
          );
        },
      );

      List<String> permissionResults = [];

      // 1. Request notification permission using CallKit method
      try {
        await requestNotificationPermission();
        permissionResults.add('✓ Notification permission requested');
      } catch (e) {
        permissionResults.add('✗ Notification permission failed: $e');
      }

      // 2. Request microphone permission
      PermissionStatus microphoneStatus = await Permission.microphone.request();
      if (microphoneStatus.isGranted) {
        permissionResults.add('✓ Microphone permission granted');
      } else if (microphoneStatus.isDenied) {
        permissionResults.add('✗ Microphone permission denied');
      } else if (microphoneStatus.isPermanentlyDenied) {
        permissionResults.add('✗ Microphone permission permanently denied');
      }

      // 3. Platform-specific permissions
      if (Platform.isAndroid) {
        // Request phone permission for Android
        PermissionStatus phoneStatus = await Permission.phone.request();
        if (phoneStatus.isGranted) {
          permissionResults.add('✓ Phone permission granted');
        } else if (phoneStatus.isDenied) {
          permissionResults.add('✗ Phone permission denied');
        } else if (phoneStatus.isPermanentlyDenied) {
          permissionResults.add('✗ Phone permission permanently denied');
        }

        // Request full intent permission for Android (CallKit specific)
        try {
          await FlutterCallkitIncoming.requestFullIntentPermission();
          permissionResults.add('✓ Full intent permission requested');
        } catch (e) {
          permissionResults.add('✗ Full intent permission failed: $e');
        }
      } else if (Platform.isIOS) {
        permissionResults.add('✓ iOS permissions handled by system');
      }

      // 4. Optional: Camera permission (for video calls)
      PermissionStatus cameraStatus = await Permission.camera.request();
      if (cameraStatus.isGranted) {
        permissionResults.add('✓ Camera permission granted');
      } else if (cameraStatus.isDenied) {
        permissionResults.add('⚠ Camera permission denied (video calls unavailable)');
      } else if (cameraStatus.isPermanentlyDenied) {
        permissionResults.add('⚠ Camera permission permanently denied');
      }

      // Close loading dialog
      Navigator.of(context).pop();

      // Show results dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Permission Results'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: permissionResults.map((result) =>
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(result),
                  )
                ).toList(),
              ),
            ),
            actions: [
              if (permissionResults.any((result) => result.contains('permanently denied')))
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  },
                  child: const Text('Open Settings'),
                ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );

    } catch (e) {
      // Close loading dialog if it's open
      Navigator.of(context).pop();

      // Show error dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Permission Error'),
            content: Text('Failed to request permissions: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  Future<dynamic> initCurrentCall() async {
    await requestNotificationPermission();
    //check current call from pushkit if possible
    var calls = await FlutterCallkitIncoming.activeCalls();
    if (calls is List) {
      if (calls.isNotEmpty) {
        print('DATA: $calls');
        _currentUuid = calls[0]['id'];
        return calls[0];
      } else {
        _currentUuid = "";
        return null;
      }
    }
  }

  Future<void> makeFakeCallInComing() async {
    await Future.delayed(const Duration(seconds: 10), () async {
      _currentUuid = _uuid.v4();

      final params = CallKitParams(
        id: _currentUuid,
        nameCaller: 'Hien Nguyen',
        appName: 'Callkit',
        avatar: 'https://i.pravatar.cc/100',
        handle: '0123456789',
        type: 0,
        duration: 30000,
        textAccept: 'Accept',
        textDecline: 'Decline',
        missedCallNotification: const NotificationParams(
          showNotification: true,
          isShowCallback: true,
          subtitle: 'Missed call',
          callbackText: 'Call back',
        ),
        callingNotification: const NotificationParams(
          showNotification: true,
          isShowCallback: true,
          subtitle: 'Calling...',
          callbackText: 'Hang Up',
        ),
        extra: <String, dynamic>{'userId': '1a2b3c4d'},
        headers: <String, dynamic>{'apiKey': 'Abc@123!', 'platform': 'flutter'},
        android: const AndroidParams(
          isCustomNotification: true,
          isShowLogo: true,
          isShowCallID: true,
          logoUrl: 'assets/test.png',
          ringtonePath: 'system_ringtone_default',
          backgroundColor: '#0955fa',
          backgroundUrl: 'assets/test.png',
          actionColor: '#4CAF50',
          textColor: '#ffffff',
          incomingCallNotificationChannelName: 'Incoming Call',
          missedCallNotificationChannelName: 'Missed Call',
          isImportant: true,
          isBot: false,
        ),
        ios: const IOSParams(
          iconName: 'CallKitLogo',
          handleType: '',
          supportsVideo: true,
          maximumCallGroups: 2,
          maximumCallsPerCallGroup: 1,
          audioSessionMode: 'default',
          audioSessionActive: true,
          audioSessionPreferredSampleRate: 44100.0,
          audioSessionPreferredIOBufferDuration: 0.005,
          supportsDTMF: true,
          supportsHolding: true,
          supportsGrouping: false,
          supportsUngrouping: false,
          ringtonePath: 'system_ringtone_default',
        ),
      );
      await FlutterCallkitIncoming.showCallkitIncoming(params);
    });
  }

  Future<void> endCurrentCall() async {
    initCurrentCall();
    await FlutterCallkitIncoming.endCall(_currentUuid!);
  }

  Future<void> activeCalls() async {
    var calls = await FlutterCallkitIncoming.activeCalls();
    print(calls);
  }

  Future<void> endAllCalls() async {
    await FlutterCallkitIncoming.endAllCalls();
  }

  Future<void> getDevicePushTokenVoIP() async {
    var devicePushTokenVoIP =
        await FlutterCallkitIncoming.getDevicePushTokenVoIP();
    print(devicePushTokenVoIP);
  }

  Future<void> listenerEvent(void Function(CallEvent) callback) async {
    try {
      FlutterCallkitIncoming.onEvent.listen((event) async {
        switch (event!.event) {
          case Event.actionCallIncoming:
            // TODO: received an incoming call
            break;
          case Event.actionCallStart:
            // TODO: started an outgoing call
            // TODO: show screen calling in Flutter
            break;
          case Event.actionCallAccept:
            // TODO: accepted an incoming call
            // TODO: show screen calling in Flutter
            NavigationService.instance
                .pushNamedIfNotCurrent(AppRoute.callingPage, args: event.body);
            break;
          case Event.actionCallDecline:
            // TODO: declined an incoming call
            break;
          case Event.actionCallEnded:
            // TODO: ended an incoming/outgoing call
            break;
          case Event.actionCallTimeout:
            // TODO: missed an incoming call
            break;
          case Event.actionCallCallback:
            // TODO: only Android - click action `Call back` from missed call notification
            break;
          case Event.actionCallToggleHold:
            // TODO: only iOS
            break;
          case Event.actionCallToggleMute:
            // TODO: only iOS
            break;
          case Event.actionCallToggleDmtf:
            // TODO: only iOS
            break;
          case Event.actionCallToggleGroup:
            // TODO: only iOS
            break;
          case Event.actionCallToggleAudioSession:
            // TODO: only iOS
            break;
          case Event.actionDidUpdateDevicePushTokenVoip:
            // TODO: only iOS
            break;
          case Event.actionCallCustom:
            break;
        }
        callback(event);
      });
    } on Exception catch (e) {
      print(e);
    }
  }

  Future<void> _registerPushToken() async {
    await Future.delayed(Duration(seconds: 1));
    String? token;
    String platform;
    if (Platform.isAndroid) {
      token = await FirebaseMessaging.instance.getToken();
      platform = "android";
    } else {
      token = await FlutterCallkitIncoming.getDevicePushTokenVoIP();
      platform = "ios";
    }
    if (token != null) {
      try {
        final response = await ApiService.registerNotificationToken(
          userId: userId, // Use a random int for user_id
          notificationToken: token,
          platform: platform,
        );
        if (response.statusCode == 200) {
          setState(() {
            _registerTokenStatus = "Push token registered successfully";
          });
          debugPrint("Push token registered successfully");
        } else {
          setState(() {
            _registerTokenStatus = "Failed to register push token: ${response.body}";
          });
          debugPrint("Failed to register push token ${response.body}");
        }
      } catch (e) {
        setState(() {
          _registerTokenStatus = "Error registering push token: $e";
        });
        debugPrint("Error registering push token: $e");
      }
    }
  }

  Future<void> _initiateCall() async {
    final calleeIdText = _calleeController.text.trim();
    if (calleeIdText.isEmpty || int.tryParse(calleeIdText) == null) {
      setState(() {
        _callStatus = 'Please enter a valid callee ID.';
      });
      return;
    }
    final calleeId = int.parse(calleeIdText);
    setState(() {
      _callStatus = 'Calling...';
    });
    try {
      final response = await ApiService.initiateCall(
        callerId: userId,
        calleeId: calleeId,
      );
      if (response.statusCode == 200) {
        setState(() {
          _callStatus = 'Call initiated successfully!';
        });

        // Parse the API response and navigate to calling page
        try {
          final responseData = jsonDecode(response.body);
          final callId = responseData['call_id'] as String;

          // Create CallKit params for the outgoing call
          final params = CallKitParams(
            id: callId,
            nameCaller: 'You',
            appName: 'Callkit',
            avatar: 'https://i.pravatar.cc/100',
            handle: calleeIdText,
            type: 0, // Audio call
            duration: 30000,
            textAccept: 'Accept',
            textDecline: 'Decline',
            extra: <String, dynamic>{
              'platform': 'flutter'
            },
            headers: <String, dynamic>{'apiKey': 'Abc@123!', 'platform': 'flutter'},
            android: const AndroidParams(
              isCustomNotification: true,
              isShowLogo: true,
              isShowCallID: true,
              logoUrl: 'assets/test.png',
              ringtonePath: 'system_ringtone_default',
              backgroundColor: '#0955fa',
              backgroundUrl: 'assets/test.png',
              actionColor: '#4CAF50',
              textColor: '#ffffff',
              incomingCallNotificationChannelName: 'Incoming Call',
              missedCallNotificationChannelName: 'Missed Call',
              isImportant: true,
              isBot: false,
            ),
            ios: const IOSParams(
              iconName: 'CallKitLogo',
              handleType: '',
              supportsVideo: false,
              maximumCallGroups: 2,
              maximumCallsPerCallGroup: 1,
              audioSessionMode: 'default',
              audioSessionActive: true,
              audioSessionPreferredSampleRate: 44100.0,
              audioSessionPreferredIOBufferDuration: 0.005,
              supportsDTMF: true,
              supportsHolding: true,
              supportsGrouping: false,
              supportsUngrouping: false,
              ringtonePath: 'system_ringtone_default',
            ),
          );

          // Navigate directly to calling page
          NavigationService.instance.pushNamedIfNotCurrent(
            AppRoute.callingPage,
            args: params.toJson()
          );

        } catch (e) {
          setState(() {
            _callStatus = 'Error parsing response: $e';
          });
        }
      } else {
        setState(() {
          _callStatus = 'Failed to initiate call: ${response.body}';
        });
      }
    } catch (e) {
      setState(() {
        _callStatus = 'Error initiating call: $e';
      });
    }
  }

  void onEvent(CallEvent event) {
    if (!mounted) return;
    setState(() {
      textEvents += '---\n${event.toString()}\n';
    });
  }
}
